from datasets import load_from_disk
import json

# 加载本地Arrow格式数据集
dataset_path = r'd:\Projects\Demo\HKAIR-Lab'
dataset = load_from_disk(dataset_path)

# 转换训练集为JSONL
with open('hk-o1aw-sft-16_k-train.jsonl', 'w', encoding='utf-8') as f:
    for item in dataset['train']:
        json.dump(item, f, ensure_ascii=False)
        f.write('\n')

# 转换测试集为JSONL（可选）
with open('hk-o1aw-sft-16_k-test.jsonl', 'w', encoding='utf-8') as f:
    for item in dataset['test']:
        json.dump(item, f, ensure_ascii=False)
        f.write('\n')

print('转换完成！JSONL文件已保存到当前目录')