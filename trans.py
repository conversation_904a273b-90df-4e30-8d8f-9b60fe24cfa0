from datasets import Dataset
import json
import os

# 直接加载Arrow文件
train_arrow_path = r'd:\Projects\Demo\HKAIR-Lab\default\0.0.0\67c330a75827dcadfd624e80ceedbea41a0dc666\hk-o1aw-sft-16_k-train.arrow'
test_arrow_path = r'd:\Projects\Demo\HKAIR-Lab\default\0.0.0\67c330a75827dcadfd624e80ceedbea41a0dc666\hk-o1aw-sft-16_k-test.arrow'

# 加载训练集和测试集
train_dataset = Dataset.from_file(train_arrow_path)
test_dataset = Dataset.from_file(test_arrow_path)

print(f"数据集信息:")
print(f"- 训练集样本数: {len(train_dataset)}")
print(f"- 测试集样本数: {len(test_dataset)}")
print(f"- 数据集字段: {train_dataset.column_names}")

# 转换训练集为JSONL
train_output_file = 'hk-o1aw-sft-16_k-train.jsonl'
print(f"\n正在转换训练集到 {train_output_file}...")
with open(train_output_file, 'w', encoding='utf-8') as f:
    for i, item in enumerate(train_dataset):
        json.dump(item, f, ensure_ascii=False)
        f.write('\n')
        if (i + 1) % 1000 == 0:
            print(f"已处理训练集 {i + 1} 条数据")

# 转换测试集为JSONL
test_output_file = 'hk-o1aw-sft-16_k-test.jsonl'
print(f"\n正在转换测试集到 {test_output_file}...")
with open(test_output_file, 'w', encoding='utf-8') as f:
    for i, item in enumerate(test_dataset):
        json.dump(item, f, ensure_ascii=False)
        f.write('\n')
        if (i + 1) % 1000 == 0:
            print(f"已处理测试集 {i + 1} 条数据")

print(f'\n转换完成！')
print(f'- 训练集JSONL文件: {train_output_file}')
print(f'- 测试集JSONL文件: {test_output_file}')
print(f'文件已保存到当前目录: {os.getcwd()}')