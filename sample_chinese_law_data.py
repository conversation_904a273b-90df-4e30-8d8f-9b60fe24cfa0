import json
import random
import os
from datasets import Dataset

def sample_chinese_law_data(input_file, output_file, sample_size=3000, seed=42):
    """
    从chinese_law_ft_train.jsonl中随机抽取指定数量的数据
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
        sample_size: 抽取的样本数量
        seed: 随机种子，确保结果可重现
    """
    print(f"开始从 {input_file} 随机抽取 {sample_size} 条数据...")
    
    # 设置随机种子
    random.seed(seed)
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        return None
    
    # 读取所有数据
    all_data = []
    total_lines = 0
    
    print("正在读取数据...")
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            total_lines += 1
            try:
                data = json.loads(line.strip())
                all_data.append(data)
                
                if line_num % 10000 == 0:
                    print(f"已读取 {line_num} 行数据")
                    
            except json.JSONDecodeError as e:
                print(f"第 {line_num} 行JSON解析错误: {e}")
                continue
    
    print(f"数据读取完成！总共 {len(all_data)} 条有效数据")
    
    # 检查数据量是否足够
    if len(all_data) < sample_size:
        print(f"警告: 数据总量 ({len(all_data)}) 小于请求的抽取数量 ({sample_size})")
        sample_size = len(all_data)
        print(f"将抽取全部 {sample_size} 条数据")
    
    # 随机抽取数据
    print(f"正在随机抽取 {sample_size} 条数据...")
    sampled_data = random.sample(all_data, sample_size)
    
    # 保存抽取的数据
    print(f"正在保存到 {output_file}...")
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in sampled_data:
            json.dump(item, f, ensure_ascii=False)
            f.write('\n')
    
    # 统计结果
    print(f"\n抽取完成！")
    print(f"- 原始数据总数: {len(all_data)}")
    print(f"- 抽取数据数量: {sample_size}")
    print(f"- 抽取比例: {sample_size/len(all_data)*100:.2f}%")
    print(f"- 输出文件: {output_file}")
    print(f"- 文件大小: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
    
    return sampled_data

def preview_data_format(data, num_samples=3):
    """
    预览数据格式
    """
    print(f"\n数据格式预览 (显示前{num_samples}条):")
    print("=" * 80)
    
    for i, sample in enumerate(data[:num_samples]):
        print(f"\n样本 {i+1}:")
        for key, value in sample.items():
            if isinstance(value, str) and len(value) > 150:
                print(f"  {key}: {value[:150]}...")
            else:
                print(f"  {key}: {value}")
        print("-" * 40)

def convert_to_unsloth_format(sampled_data, output_prefix="chinese_law_sample"):
    """
    将抽取的数据转换为Unsloth格式
    """
    print(f"\n开始转换为Unsloth格式...")
    
    conversations_data = []
    
    for i, item in enumerate(sampled_data):
        try:
            # 根据数据集的实际字段结构进行转换
            if 'instruction' in item and 'output' in item:
                # 标准指令格式
                user_content = item['instruction']
                if 'input' in item and item['input'].strip():
                    # 如果有input字段且不为空，则合并到instruction中
                    user_content = f"{item['instruction']}\n\n{item['input']}"
                
                conversation = {
                    "conversations": [
                        {"role": "user", "content": user_content},
                        {"role": "assistant", "content": item['output']}
                    ]
                }
            elif 'input' in item and 'output' in item:
                # 输入输出格式
                conversation = {
                    "conversations": [
                        {"role": "user", "content": item['input']},
                        {"role": "assistant", "content": item['output']}
                    ]
                }
            elif 'question' in item and 'answer' in item:
                # 问答格式
                conversation = {
                    "conversations": [
                        {"role": "user", "content": item['question']},
                        {"role": "assistant", "content": item['answer']}
                    ]
                }
            else:
                # 尝试自动检测字段
                keys = list(item.keys())
                if len(keys) >= 2:
                    # 假设第一个字段是问题，最后一个是答案
                    conversation = {
                        "conversations": [
                            {"role": "user", "content": str(item[keys[0]])},
                            {"role": "assistant", "content": str(item[keys[-1]])}
                        ]
                    }
                else:
                    print(f"警告: 无法识别第 {i+1} 条数据的格式，跳过")
                    continue
            
            conversations_data.append(conversation)
            
        except Exception as e:
            print(f"处理第 {i+1} 条数据时出错: {e}")
            continue
    
    # 保存Unsloth格式
    unsloth_file = f'{output_prefix}_unsloth.jsonl'
    with open(unsloth_file, 'w', encoding='utf-8') as f:
        for item in conversations_data:
            json.dump(item, f, ensure_ascii=False)
            f.write('\n')
    
    # 创建Dataset
    dataset = Dataset.from_list(conversations_data)
    dataset_dir = f"./{output_prefix.replace('_', '-').title()}-Unsloth"
    dataset.save_to_disk(dataset_dir)
    
    print(f"Unsloth格式转换完成！")
    print(f"- 转换后数据: {len(conversations_data)} 条")
    print(f"- JSONL文件: {unsloth_file}")
    print(f"- Arrow数据集: {dataset_dir}")
    print(f"- 文件大小: {os.path.getsize(unsloth_file) / (1024*1024):.2f} MB")
    
    # 预览转换后的数据
    print(f"\nUnsloth格式数据预览:")
    for i, sample in enumerate(conversations_data[:2]):
        print(f"\n样本 {i+1}:")
        for conv in sample['conversations']:
            role = conv['role']
            content = conv['content'][:150] + "..." if len(conv['content']) > 150 else conv['content']
            print(f"  {role}: {content}")
    
    return conversations_data, unsloth_file, dataset_dir

def main():
    input_file = "chinese_law_ft_train.jsonl"
    output_file = "chinese_law_sample_3k.jsonl"
    sample_size = 3000
    
    print("中国法律数据集随机抽取工具")
    print("=" * 50)
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        print("请确保已下载chinese_law_ft_dataset数据集")
        return
    
    # 抽取数据
    sampled_data = sample_chinese_law_data(input_file, output_file, sample_size)
    
    if sampled_data is None:
        return
    
    # 预览数据格式
    preview_data_format(sampled_data)
    
    # 询问是否转换为Unsloth格式
    convert_choice = input(f"\n是否转换为Unsloth格式？(y/n, 默认y): ").strip().lower()
    if convert_choice != 'n':
        conversations_data, unsloth_file, dataset_dir = convert_to_unsloth_format(
            sampled_data, "chinese_law_sample_3k"
        )
        
        print(f"\n✅ 数据抽取和转换完成！")
        print(f"\n生成的文件:")
        print(f"1. {output_file} - 原格式抽取数据")
        print(f"2. {unsloth_file} - Unsloth格式数据")
        print(f"3. {dataset_dir}/ - Arrow格式数据集")
    else:
        print(f"\n✅ 数据抽取完成！")
        print(f"输出文件: {output_file}")
    
    print(f"\n数据集特点:")
    print(f"- 随机抽取的3000条中文法律数据")
    print(f"- 适用于法律领域微调训练")
    print(f"- 可直接用于Unsloth训练框架")

if __name__ == "__main__":
    main()
