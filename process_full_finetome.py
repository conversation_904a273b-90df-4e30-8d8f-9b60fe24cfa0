import json
from datasets import Dataset
import os
import argparse

def convert_full_finetome_to_unsloth_format(max_samples=None):
    """
    将完整的FineTome-100k数据集转换为适合Unsloth训练的格式
    
    Args:
        max_samples: 最大处理样本数，None表示处理全部
    """
    print(f"开始转换FineTome-100k数据集为Unsloth格式...")
    if max_samples:
        print(f"限制处理样本数: {max_samples}")
    else:
        print("处理全部100k样本")
    
    # 读取JSONL文件
    conversations_data = []
    
    with open('finetome-100k.jsonl', 'r', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if max_samples and i >= max_samples:
                break
                
            data = json.loads(line.strip())
            conversations = data['conversations']
            
            # 转换为Unsloth格式
            unsloth_conversations = []
            for conv in conversations:
                if conv['from'] == 'human':
                    role = 'user'
                elif conv['from'] == 'gpt':
                    role = 'assistant'
                else:
                    continue  # 跳过其他角色
                
                unsloth_conversations.append({
                    "role": role,
                    "content": conv['value']
                })
            
            # 只保留有效的对话（至少包含一个用户消息和一个助手回复）
            if len(unsloth_conversations) >= 2:
                conversations_data.append({
                    "conversations": unsloth_conversations
                })
            
            if (i + 1) % 5000 == 0:
                print(f"已处理 {i + 1} 条数据")
    
    print(f"转换完成！共处理了 {len(conversations_data)} 条有效对话")
    
    # 确定输出文件名
    if max_samples:
        suffix = f"{max_samples//1000}k"
    else:
        suffix = "100k"
    
    output_file = f'finetome-{suffix}-unsloth.jsonl'
    dataset_dir = f'./FineTome-{suffix}-Unsloth'
    
    # 保存为新的JSONL文件
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in conversations_data:
            json.dump(item, f, ensure_ascii=False)
            f.write('\n')
    
    print(f"Unsloth格式数据已保存到: {output_file}")
    print(f"文件大小: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
    
    # 创建Dataset对象并保存
    dataset = Dataset.from_list(conversations_data)
    dataset.save_to_disk(dataset_dir)
    print(f"Arrow格式数据集已保存到: {dataset_dir}")
    
    # 显示数据样本
    print("\n数据样本预览:")
    for i, sample in enumerate(conversations_data[:2]):
        print(f"\n样本 {i+1}:")
        for j, conv in enumerate(sample['conversations']):
            role = conv['role']
            content = conv['content'][:150] + "..." if len(conv['content']) > 150 else conv['content']
            print(f"  {role}: {content}")
    
    return dataset, output_file, dataset_dir

def create_training_script(dataset_dir, output_name="finetome_model"):
    """
    创建针对特定数据集的训练脚本
    """
    script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FineTome数据集Unsloth微调脚本
使用数据集: {dataset_dir}
"""

from unsloth import FastLanguageModel
from datasets import load_from_disk
from trl import SFTTrainer
from transformers import TrainingArguments
import torch
import os

# 设置环境变量
os.environ["CUDA_VISIBLE_DEVICES"] = "1"  # 根据您的GPU设置调整

def main():
    print("开始FineTome数据集微调...")
    
    # 1. 模型配置
    max_seq_length = 2048
    dtype = None
    load_in_4bit = True
    
    # 2. 加载模型
    print("加载模型...")
    model, tokenizer = FastLanguageModel.from_pretrained(
        model_name = "./Qwen3-32B-unsloth-bnb-4bit",
        max_seq_length = max_seq_length,
        dtype = dtype,
        load_in_4bit = load_in_4bit,
    )
    
    # 3. 添加LoRA适配器
    print("添加LoRA适配器...")
    model = FastLanguageModel.get_peft_model(
        model,
        r = 16,
        target_modules = ["q_proj", "k_proj", "v_proj", "o_proj",
                          "gate_proj", "up_proj", "down_proj",],
        lora_alpha = 16,
        lora_dropout = 0,
        bias = "none",
        use_gradient_checkpointing = "unsloth",
        random_state = 3407,
        use_rslora = False,
        loftq_config = None,
    )
    
    # 4. 加载数据集
    print("加载数据集...")
    dataset = load_from_disk("{dataset_dir}")
    print(f"数据集大小: {{len(dataset)}}")
    
    # 5. 数据格式化函数
    def formatting_prompts_func(examples):
        convos = examples["conversations"]
        texts = []
        for convo in convos:
            text = tokenizer.apply_chat_template(
                convo, 
                tokenize = False, 
                add_generation_prompt = False
            )
            texts.append(text)
        return {{"text": texts}}
    
    print("格式化数据集...")
    dataset = dataset.map(formatting_prompts_func, batched=True)
    
    # 6. 训练配置
    print("配置训练参数...")
    trainer = SFTTrainer(
        model = model,
        tokenizer = tokenizer,
        train_dataset = dataset,
        dataset_text_field = "text",
        max_seq_length = max_seq_length,
        dataset_num_proc = 2,
        packing = False,
        args = TrainingArguments(
            per_device_train_batch_size = 2,
            gradient_accumulation_steps = 4,
            warmup_steps = 10,
            max_steps = 500,  # 根据数据集大小调整
            learning_rate = 2e-4,
            fp16 = not torch.cuda.is_bf16_supported(),
            bf16 = torch.cuda.is_bf16_supported(),
            logging_steps = 10,
            optim = "adamw_8bit",
            weight_decay = 0.01,
            lr_scheduler_type = "linear",
            seed = 3407,
            output_dir = "outputs",
            save_steps = 100,
            save_total_limit = 3,
            dataloader_num_workers = 2,
        ),
    )
    
    # 7. 显示GPU内存使用情况
    gpu_stats = torch.cuda.get_device_properties(0)
    start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)
    max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)
    print(f"GPU = {{gpu_stats.name}}. Max memory = {{max_memory}} GB.")
    print(f"{{start_gpu_memory}} GB of memory reserved.")
    
    # 8. 开始训练
    print("开始训练...")
    trainer_stats = trainer.train()
    
    # 9. 显示训练后的内存使用情况
    used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)
    used_memory_for_lora = round(used_memory - start_gpu_memory, 3)
    used_percentage = round(used_memory / max_memory * 100, 3)
    lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)
    print(f"{{used_memory}} GB of memory reserved.")
    print(f"{{used_memory_for_lora}} GB used for LoRA ({{lora_percentage}}%).")
    print(f"{{used_percentage}}% of total memory used.")
    
    # 10. 保存模型
    print("保存模型...")
    model.save_pretrained("{output_name}")
    tokenizer.save_pretrained("{output_name}")
    
    print(f"训练完成！模型已保存到: {output_name}")
    
    # 11. 测试模型
    print("\\n测试微调后的模型...")
    FastLanguageModel.for_inference(model)
    
    messages = [
        {{"role": "user", "content": "请解释什么是机器学习？"}}
    ]
    
    inputs = tokenizer.apply_chat_template(
        messages,
        tokenize = True,
        add_generation_prompt = True,
        return_tensors = "pt"
    ).to("cuda")
    
    outputs = model.generate(
        input_ids = inputs,
        max_new_tokens = 256,
        use_cache = True,
        temperature = 0.7,
        top_p = 0.9,
    )
    
    response = tokenizer.batch_decode(outputs)
    print("模型回复:")
    print(response[0])

if __name__ == "__main__":
    main()
'''
    
    script_filename = f'train_{output_name}.py'
    with open(script_filename, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"训练脚本已保存到: {script_filename}")
    return script_filename

def main():
    parser = argparse.ArgumentParser(description='处理FineTome-100k数据集')
    parser.add_argument('--max_samples', type=int, default=None, 
                       help='最大处理样本数 (默认: 处理全部)')
    parser.add_argument('--model_name', type=str, default='finetome_model',
                       help='输出模型名称 (默认: finetome_model)')
    
    args = parser.parse_args()
    
    # 转换数据集
    dataset, jsonl_file, dataset_dir = convert_full_finetome_to_unsloth_format(args.max_samples)
    
    # 创建训练脚本
    script_file = create_training_script(dataset_dir, args.model_name)
    
    print(f"\\n✅ 处理完成！")
    print(f"\\n生成的文件:")
    print(f"1. {jsonl_file} - JSONL格式数据")
    print(f"2. {dataset_dir}/ - Arrow格式数据集")
    print(f"3. {script_file} - 训练脚本")
    
    print(f"\\n使用方法:")
    print(f"python {script_file}")

if __name__ == "__main__":
    main()
