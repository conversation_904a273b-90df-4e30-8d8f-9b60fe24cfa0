import json
import pandas as pd
import os

def export_jsonl_to_excel(input_file, output_file):
    """
    将JSONL文件导出为Excel表格
    """
    print(f"开始将 {input_file} 导出为Excel表格...")
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        return
    
    data_rows = []
    
    # 读取JSONL文件
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                conversations = data['conversations']
                
                # 提取用户问题和助手回答
                user_content = conversations[0]['content']
                assistant_content = conversations[1]['content']
                
                # 如果助手回答包含thinking，分离thinking和答案
                if '<think>' in assistant_content and '</think>' in assistant_content:
                    think_start = assistant_content.find('<think>')
                    think_end = assistant_content.find('</think>') + 8
                    thinking_part = assistant_content[think_start:think_end]
                    answer_part = assistant_content[think_end:].strip()
                    
                    # 清理thinking标签
                    thinking_clean = thinking_part.replace('<think>', '').replace('</think>', '').strip()
                else:
                    thinking_clean = ""
                    answer_part = assistant_content
                
                # 创建数据行
                row = {
                    '序号': line_num,
                    '用户问题': user_content,
                    '思考过程': thinking_clean,
                    '助手回答': answer_part,
                    '问题长度': len(user_content),
                    '回答长度': len(answer_part),
                    '思考长度': len(thinking_clean) if thinking_clean else 0,
                    '总长度': len(user_content) + len(answer_part) + len(thinking_clean)
                }
                
                data_rows.append(row)
                
                if line_num % 500 == 0:
                    print(f"已处理 {line_num} 条数据")
                    
            except json.JSONDecodeError as e:
                print(f"第 {line_num} 行JSON解析错误: {e}")
                continue
    
    # 创建DataFrame
    df = pd.DataFrame(data_rows)
    
    # 导出为Excel
    print(f"正在导出到 {output_file}...")
    
    # 使用ExcelWriter来设置格式
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 写入主数据表
        df.to_excel(writer, sheet_name='法律问答数据', index=False)
        
        # 获取工作表对象
        worksheet = writer.sheets['法律问答数据']
        
        # 设置列宽
        column_widths = {
            'A': 8,   # 序号
            'B': 50,  # 用户问题
            'C': 60,  # 思考过程
            'D': 50,  # 助手回答
            'E': 12,  # 问题长度
            'F': 12,  # 回答长度
            'G': 12,  # 思考长度
            'H': 12   # 总长度
        }
        
        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width
        
        # 设置文本换行
        from openpyxl.styles import Alignment
        wrap_alignment = Alignment(wrap_text=True, vertical='top')
        
        for row in worksheet.iter_rows(min_row=2):  # 跳过标题行
            for cell in row:
                if cell.column_letter in ['B', 'C', 'D']:  # 问题、思考、回答列
                    cell.alignment = wrap_alignment
    
    # 创建统计信息表
    create_statistics_sheet(df, output_file)
    
    print(f"Excel导出完成！")
    print(f"- 数据条数: {len(df)}")
    print(f"- 输出文件: {output_file}")
    print(f"- 文件大小: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
    
    # 显示基本统计信息
    print(f"\n数据统计:")
    print(f"- 平均问题长度: {df['问题长度'].mean():.1f} 字符")
    print(f"- 平均回答长度: {df['回答长度'].mean():.1f} 字符")
    if df['思考长度'].sum() > 0:
        print(f"- 平均思考长度: {df['思考长度'].mean():.1f} 字符")
    print(f"- 最长问题: {df['问题长度'].max()} 字符")
    print(f"- 最长回答: {df['回答长度'].max()} 字符")

def create_statistics_sheet(df, output_file):
    """
    创建统计信息表
    """
    # 计算统计信息
    stats_data = {
        '统计项目': [
            '总数据条数',
            '平均问题长度',
            '平均回答长度',
            '平均思考长度',
            '最短问题长度',
            '最长问题长度',
            '最短回答长度',
            '最长回答长度',
            '包含思考过程的数据',
            '平均总长度'
        ],
        '数值': [
            len(df),
            round(df['问题长度'].mean(), 1),
            round(df['回答长度'].mean(), 1),
            round(df['思考长度'].mean(), 1),
            df['问题长度'].min(),
            df['问题长度'].max(),
            df['回答长度'].min(),
            df['回答长度'].max(),
            len(df[df['思考长度'] > 0]),
            round(df['总长度'].mean(), 1)
        ],
        '单位': [
            '条',
            '字符',
            '字符',
            '字符',
            '字符',
            '字符',
            '字符',
            '字符',
            '条',
            '字符'
        ]
    }
    
    stats_df = pd.DataFrame(stats_data)
    
    # 追加到Excel文件
    with pd.ExcelWriter(output_file, mode='a', engine='openpyxl') as writer:
        stats_df.to_excel(writer, sheet_name='数据统计', index=False)
        
        # 设置统计表格式
        worksheet = writer.sheets['数据统计']
        worksheet.column_dimensions['A'].width = 20
        worksheet.column_dimensions['B'].width = 15
        worksheet.column_dimensions['C'].width = 10

def preview_excel_data(df, num_samples=3):
    """
    预览将要导出的数据
    """
    print(f"\n将要导出的数据预览 (显示前{num_samples}条):")
    print("=" * 80)
    
    for i in range(min(num_samples, len(df))):
        row = df.iloc[i]
        print(f"\n第 {i+1} 条数据:")
        print(f"用户问题: {row['用户问题'][:100]}...")
        if row['思考过程']:
            print(f"思考过程: {row['思考过程'][:100]}...")
        print(f"助手回答: {row['助手回答'][:100]}...")
        print(f"长度统计: 问题{row['问题长度']}字符, 回答{row['回答长度']}字符, 思考{row['思考长度']}字符")
        print("-" * 40)

def main():
    input_file = "chinese_law_sample_3k_unsloth.jsonl"
    output_file = "chinese_law_sample_3k.xlsx"
    
    print("中国法律3K数据集Excel导出工具")
    print("=" * 50)
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        return
    
    # 检查是否安装了必要的库
    try:
        import openpyxl
    except ImportError:
        print("错误: 需要安装openpyxl库")
        print("请运行: pip install openpyxl")
        return
    
    # 导出Excel
    export_jsonl_to_excel(input_file, output_file)
    
    print(f"\n✅ Excel导出完成！")
    print(f"\n生成的文件:")
    print(f"- {output_file}")
    
    print(f"\nExcel文件包含:")
    print(f"- 法律问答数据表: 包含所有问答数据")
    print(f"- 数据统计表: 包含统计信息")
    
    print(f"\n表格列说明:")
    print(f"- 序号: 数据编号")
    print(f"- 用户问题: 法律问题内容")
    print(f"- 思考过程: AI的推理过程(如果有)")
    print(f"- 助手回答: 法律问题的回答")
    print(f"- 问题长度/回答长度/思考长度: 字符数统计")
    print(f"- 总长度: 所有内容的总字符数")

if __name__ == "__main__":
    main()
