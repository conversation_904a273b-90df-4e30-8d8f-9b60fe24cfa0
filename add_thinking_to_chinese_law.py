import json
import random
from datasets import Dataset
import os

def generate_thinking_process(user_question, assistant_answer):
    """
    为法律问答生成思考过程
    """
    # 分析问题类型
    question_lower = user_question.lower()
    
    # 根据问题内容生成相应的思考模板
    thinking_templates = []
    
    # 1. 法律条文分析类
    if any(keyword in question_lower for keyword in ['法律', '条例', '规定', '法规', '条文']):
        thinking_templates.append(
            "**分析相关法律条文**\n"
            "首先需要识别这个问题涉及的具体法律法规。从问题描述来看，主要涉及相关法律条文的适用和解释。\n\n"
            "**理解法律适用条件**\n"
            "需要分析法律条文的适用条件、构成要件以及法律后果。\n\n"
            "**考虑实际情况**\n"
            "结合具体案例情况，分析如何将抽象的法律条文应用到具体的实际情况中。"
        )
    
    # 2. 案例分析类
    if any(keyword in question_lower for keyword in ['案例', '情况', '行为', '应对', '处理']):
        thinking_templates.append(
            "**案例事实分析**\n"
            "首先梳理案例中的关键事实，识别各方当事人的行为和责任。\n\n"
            "**法律关系识别**\n"
            "分析案例中涉及的法律关系，确定适用的法律规范。\n\n"
            "**解决方案评估**\n"
            "基于法律规定，评估不同的解决方案和应对措施。"
        )
    
    # 3. 程序流程类
    if any(keyword in question_lower for keyword in ['程序', '流程', '步骤', '如何', '怎样']):
        thinking_templates.append(
            "**程序要求分析**\n"
            "分析相关法律程序的具体要求和步骤。\n\n"
            "**关键环节识别**\n"
            "识别程序中的关键环节和注意事项。\n\n"
            "**实操指导**\n"
            "提供具体的操作指导和建议。"
        )
    
    # 4. 权利义务类
    if any(keyword in question_lower for keyword in ['权利', '义务', '责任', '权限', '职权']):
        thinking_templates.append(
            "**权利义务分析**\n"
            "分析各方当事人的权利和义务关系。\n\n"
            "**责任承担**\n"
            "确定责任承担的方式和范围。\n\n"
            "**救济途径**\n"
            "分析可能的救济途径和保护措施。"
        )
    
    # 5. 违法违规类
    if any(keyword in question_lower for keyword in ['违反', '违法', '违规', '禁止', '不得']):
        thinking_templates.append(
            "**违法行为认定**\n"
            "分析行为是否构成违法，违反了哪些具体规定。\n\n"
            "**法律后果评估**\n"
            "评估违法行为可能面临的法律后果和责任。\n\n"
            "**纠正措施**\n"
            "提出相应的纠正措施和预防建议。"
        )
    
    # 如果没有匹配的模板，使用通用模板
    if not thinking_templates:
        thinking_templates.append(
            "**问题分析**\n"
            "首先需要理解问题的核心要点和法律背景。\n\n"
            "**法律适用**\n"
            "分析适用的法律法规和相关规定。\n\n"
            "**解决方案**\n"
            "基于法律分析，提出合理的解决方案。"
        )
    
    # 随机选择一个模板
    base_thinking = random.choice(thinking_templates)
    
    # 根据回答内容添加具体的法律条文引用
    if '第' in assistant_answer and '条' in assistant_answer:
        base_thinking += "\n\n**具体法条分析**\n根据相关法律条文的规定，需要仔细分析条文的具体内容和适用范围。"
    
    # 如果回答中提到具体的法律后果
    if any(keyword in assistant_answer for keyword in ['责任', '处罚', '后果', '承担']):
        base_thinking += "\n\n**法律后果评估**\n需要全面评估可能产生的法律后果，包括民事责任、行政责任等。"
    
    # 如果回答中提到程序性内容
    if any(keyword in assistant_answer for keyword in ['程序', '申请', '决定', '审查']):
        base_thinking += "\n\n**程序合规性**\n确保所有程序都符合法律规定，避免程序性错误。"
    
    return base_thinking

def add_thinking_to_dataset(input_file, output_file):
    """
    为数据集添加thinking推理过程
    """
    print(f"开始为 {input_file} 添加thinking推理过程...")
    
    # 设置随机种子
    random.seed(42)
    
    enhanced_data = []
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                conversations = data['conversations']
                
                # 获取用户问题和助手回答
                user_content = conversations[0]['content']
                assistant_content = conversations[1]['content']
                
                # 生成thinking过程
                thinking_process = generate_thinking_process(user_content, assistant_content)
                
                # 构建新的助手回答（包含thinking）
                enhanced_assistant_content = f"<think>\n{thinking_process}\n</think>\n\n{assistant_content}"
                
                # 创建新的对话数据
                enhanced_conversation = {
                    "conversations": [
                        {"role": "user", "content": user_content},
                        {"role": "assistant", "content": enhanced_assistant_content}
                    ]
                }
                
                enhanced_data.append(enhanced_conversation)
                
                if line_num % 500 == 0:
                    print(f"已处理 {line_num} 条数据")
                    
            except json.JSONDecodeError as e:
                print(f"第 {line_num} 行JSON解析错误: {e}")
                continue
    
    # 保存增强后的数据
    print(f"正在保存到 {output_file}...")
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in enhanced_data:
            json.dump(item, f, ensure_ascii=False)
            f.write('\n')
    
    print(f"thinking添加完成！")
    print(f"- 处理数据条数: {len(enhanced_data)}")
    print(f"- 输出文件: {output_file}")
    print(f"- 文件大小: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
    
    return enhanced_data

def create_enhanced_dataset(enhanced_data, dataset_name):
    """
    创建包含thinking的Arrow格式数据集
    """
    print(f"创建Arrow格式数据集...")
    
    # 创建Dataset
    dataset = Dataset.from_list(enhanced_data)
    dataset_dir = f"./{dataset_name}"
    dataset.save_to_disk(dataset_dir)
    
    print(f"Arrow格式数据集已保存到: {dataset_dir}")
    return dataset_dir

def preview_enhanced_data(enhanced_data, num_samples=2):
    """
    预览增强后的数据
    """
    print(f"\n增强后数据预览 (显示前{num_samples}条):")
    print("=" * 80)
    
    for i, sample in enumerate(enhanced_data[:num_samples]):
        print(f"\n样本 {i+1}:")
        for conv in sample['conversations']:
            role = conv['role']
            content = conv['content']
            
            if role == "assistant" and "<think>" in content:
                # 分离thinking和回答部分
                think_start = content.find("<think>")
                think_end = content.find("</think>") + 8
                thinking_part = content[think_start:think_end]
                answer_part = content[think_end:].strip()
                
                print(f"  {role} (thinking): {thinking_part[:200]}...")
                print(f"  {role} (answer): {answer_part[:200]}...")
            else:
                if len(content) > 200:
                    print(f"  {role}: {content[:200]}...")
                else:
                    print(f"  {role}: {content}")
        print("-" * 40)

def create_training_script(dataset_dir, model_name="chinese_law_3k_thinking_model"):
    """
    创建包含thinking的训练脚本
    """
    script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国法律3K数据集(含Thinking)Unsloth微调脚本
使用数据集: {dataset_dir}
"""

from unsloth import FastLanguageModel
from datasets import load_from_disk
from trl import SFTTrainer
from transformers import TrainingArguments
import torch
import os

def main():
    print("开始中国法律3K数据集(含Thinking)微调...")
    
    # 1. 模型配置
    max_seq_length = 2048
    dtype = None
    load_in_4bit = True
    
    # 2. 加载模型
    print("加载模型...")
    model, tokenizer = FastLanguageModel.from_pretrained(
        model_name = "./Qwen3-32B-unsloth-bnb-4bit",
        max_seq_length = max_seq_length,
        dtype = dtype,
        load_in_4bit = load_in_4bit,
    )
    
    # 3. 添加LoRA适配器
    print("添加LoRA适配器...")
    model = FastLanguageModel.get_peft_model(
        model,
        r = 16,
        target_modules = ["q_proj", "k_proj", "v_proj", "o_proj",
                          "gate_proj", "up_proj", "down_proj",],
        lora_alpha = 16,
        lora_dropout = 0,
        bias = "none",
        use_gradient_checkpointing = "unsloth",
        random_state = 3407,
        use_rslora = False,
        loftq_config = None,
    )
    
    # 4. 加载数据集
    print("加载数据集...")
    dataset = load_from_disk("{dataset_dir}")
    print(f"数据集大小: {{len(dataset)}}")
    
    # 5. 数据格式化函数
    def formatting_prompts_func(examples):
        convos = examples["conversations"]
        texts = []
        for convo in convos:
            text = tokenizer.apply_chat_template(
                convo, 
                tokenize = False, 
                add_generation_prompt = False,
                enable_thinking = True  # 启用thinking模式
            )
            texts.append(text)
        return {{"text": texts}}
    
    print("格式化数据集...")
    dataset = dataset.map(formatting_prompts_func, batched=True)
    
    # 6. 训练配置
    print("配置训练参数...")
    trainer = SFTTrainer(
        model = model,
        tokenizer = tokenizer,
        train_dataset = dataset,
        dataset_text_field = "text",
        max_seq_length = max_seq_length,
        dataset_num_proc = 2,
        packing = False,
        args = TrainingArguments(
            per_device_train_batch_size = 2,
            gradient_accumulation_steps = 4,
            warmup_steps = 10,
            max_steps = 400,  # 增加训练步数以学习thinking模式
            learning_rate = 2e-4,
            fp16 = not torch.cuda.is_bf16_supported(),
            bf16 = torch.cuda.is_bf16_supported(),
            logging_steps = 10,
            optim = "adamw_8bit",
            weight_decay = 0.01,
            lr_scheduler_type = "linear",
            seed = 3407,
            output_dir = "outputs",
            save_steps = 50,
            save_total_limit = 3,
            dataloader_num_workers = 2,
        ),
    )
    
    # 7. 开始训练
    print("开始训练...")
    trainer_stats = trainer.train()
    
    # 8. 保存模型
    print("保存模型...")
    model.save_pretrained("{model_name}")
    tokenizer.save_pretrained("{model_name}")
    
    print(f"训练完成！模型已保存到: {model_name}")
    
    # 9. 测试模型
    print("\\n测试微调后的模型...")
    FastLanguageModel.for_inference(model)
    
    messages = [
        {{"role": "user", "content": "请解释合同法中违约责任的承担方式有哪些？"}}
    ]
    
    inputs = tokenizer.apply_chat_template(
        messages,
        tokenize = True,
        add_generation_prompt = True,
        enable_thinking = True,
        return_tensors = "pt"
    ).to("cuda")
    
    outputs = model.generate(
        input_ids = inputs,
        max_new_tokens = 512,
        use_cache = True,
        temperature = 0.7,
        top_p = 0.9,
    )
    
    response = tokenizer.batch_decode(outputs)
    print("模型回复:")
    print(response[0])

if __name__ == "__main__":
    main()
'''
    
    script_filename = f'train_{model_name}.py'
    with open(script_filename, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"训练脚本已保存到: {script_filename}")
    return script_filename

def main():
    input_file = "chinese_law_sample_3k_unsloth.jsonl"
    output_file = "chinese_law_sample_3k_with_thinking.jsonl"
    dataset_name = "Chinese-Law-Sample-3K-Thinking"
    
    print("中国法律3K数据集Thinking增强工具")
    print("=" * 50)
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        return
    
    # 添加thinking过程
    enhanced_data = add_thinking_to_dataset(input_file, output_file)
    
    # 预览增强后的数据
    preview_enhanced_data(enhanced_data)
    
    # 创建Arrow格式数据集
    dataset_dir = create_enhanced_dataset(enhanced_data, dataset_name)
    
    # 创建训练脚本
    script_file = create_training_script(dataset_dir)
    
    print(f"\n✅ Thinking增强完成！")
    print(f"\n生成的文件:")
    print(f"1. {output_file} - 包含thinking的JSONL数据")
    print(f"2. {dataset_dir}/ - 包含thinking的Arrow数据集")
    print(f"3. {script_file} - 专用训练脚本")
    
    print(f"\n数据集特点:")
    print(f"- 每条数据都包含详细的推理过程")
    print(f"- 使用<think></think>标签包装思考内容")
    print(f"- 适合训练具有推理能力的法律助手")
    print(f"- 支持Qwen3的thinking模式")
    
    print(f"\n使用方法:")
    print(f"python {script_file}")

if __name__ == "__main__":
    main()
