import json

def verify_hongkong_removal(filename):
    """验证文件中是否还有包含'香港'的数据"""
    hongkong_count = 0
    total_lines = 0
    
    with open(filename, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            total_lines += 1
            if '香港' in line:
                hongkong_count += 1
                print(f"发现包含'香港'的行 {line_num}: {line[:100]}...")
    
    print(f"验证结果:")
    print(f"- 总行数: {total_lines}")
    print(f"- 包含'香港'的行数: {hongkong_count}")
    
    if hongkong_count == 0:
        print("✅ 验证成功：文件中已无包含'香港'的数据")
    else:
        print("❌ 验证失败：文件中仍有包含'香港'的数据")
    
    return hongkong_count == 0

if __name__ == "__main__":
    print("验证删除结果...")
    verify_hongkong_removal("hk-o1aw-sft-16_k-train_no_hongkong.jsonl")
