import json
from datasets import Dataset

def create_simple_version():
    """创建简化版本（不包含思考过程）"""
    print("创建简化版本（仅包含最终答案）...")
    
    conversations_data = []
    
    with open('hk-o1aw-sft-16_k-train_no_hongkong.jsonl', 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                
                conversation = {
                    'conversations': [
                        {'role': 'user', 'content': data['prompt']},
                        {'role': 'assistant', 'content': data['answer']}
                    ]
                }
                conversations_data.append(conversation)
                
                if line_num % 1000 == 0:
                    print(f"已处理 {line_num} 条数据")
                    
            except json.JSONDecodeError as e:
                print(f"第 {line_num} 行JSON解析错误: {e}")
                continue
    
    # 保存JSONL文件
    output_file = 'hk_legal_unsloth_simple.jsonl'
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in conversations_data:
            json.dump(item, f, ensure_ascii=False)
            f.write('\n')
    
    # 创建Dataset
    dataset = Dataset.from_list(conversations_data)
    dataset_dir = "./HK-Legal-Unsloth-Simple"
    dataset.save_to_disk(dataset_dir)
    
    print(f"简化版本转换完成！")
    print(f"- 数据条数: {len(conversations_data)}")
    print(f"- JSONL文件: {output_file}")
    print(f"- Arrow数据集: {dataset_dir}")
    
    # 预览数据
    print(f"\n简化版本数据预览:")
    for i, sample in enumerate(conversations_data[:2]):
        print(f"\n样本 {i+1}:")
        for conv in sample['conversations']:
            role = conv['role']
            content = conv['content'][:150] + "..." if len(conv['content']) > 150 else conv['content']
            print(f"  {role}: {content}")

if __name__ == "__main__":
    create_simple_version()
