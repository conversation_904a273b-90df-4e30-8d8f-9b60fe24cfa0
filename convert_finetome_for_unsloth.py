import json
from datasets import Dataset
import os

def convert_finetome_to_unsloth_format():
    """
    将FineTome-100k数据集转换为适合Unsloth训练的格式
    """
    print("开始转换FineTome-100k数据集为Unsloth格式...")
    
    # 读取JSONL文件
    conversations_data = []
    
    with open('finetome-100k.jsonl', 'r', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if i >= 10000:  # 只处理前10000条数据作为示例，您可以根据需要调整
                break
                
            data = json.loads(line.strip())
            conversations = data['conversations']
            
            # 转换为Unsloth格式
            # FineTome格式: [{"from": "human", "value": "..."}, {"from": "gpt", "value": "..."}]
            # Unsloth格式: {"conversations": [{"role": "user", "content": "..."}, {"role": "assistant", "content": "..."}]}
            
            unsloth_conversations = []
            for conv in conversations:
                if conv['from'] == 'human':
                    role = 'user'
                elif conv['from'] == 'gpt':
                    role = 'assistant'
                else:
                    continue  # 跳过其他角色
                
                unsloth_conversations.append({
                    "role": role,
                    "content": conv['value']
                })
            
            # 只保留有效的对话（至少包含一个用户消息和一个助手回复）
            if len(unsloth_conversations) >= 2:
                conversations_data.append({
                    "conversations": unsloth_conversations
                })
            
            if (i + 1) % 1000 == 0:
                print(f"已处理 {i + 1} 条数据")
    
    print(f"转换完成！共处理了 {len(conversations_data)} 条有效对话")
    
    # 保存为新的JSONL文件
    output_file = 'finetome-10k-unsloth.jsonl'
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in conversations_data:
            json.dump(item, f, ensure_ascii=False)
            f.write('\n')
    
    print(f"Unsloth格式数据已保存到: {output_file}")
    print(f"文件大小: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
    
    # 创建Dataset对象并保存
    dataset = Dataset.from_list(conversations_data)
    dataset.save_to_disk("./FineTome-10k-Unsloth")
    print(f"Arrow格式数据集已保存到: ./FineTome-10k-Unsloth")
    
    # 显示数据样本
    print("\n数据样本预览:")
    for i, sample in enumerate(conversations_data[:2]):
        print(f"\n样本 {i+1}:")
        for j, conv in enumerate(sample['conversations']):
            role = conv['role']
            content = conv['content'][:200] + "..." if len(conv['content']) > 200 else conv['content']
            print(f"  {role}: {content}")
    
    return dataset

def create_unsloth_training_script():
    """
    创建一个Unsloth训练脚本模板
    """
    script_content = '''
# Unsloth训练脚本模板
from unsloth import FastLanguageModel
from datasets import load_from_disk
from trl import SFTTrainer
from transformers import TrainingArguments
import torch

# 1. 加载模型
max_seq_length = 2048
dtype = None
load_in_4bit = True

model, tokenizer = FastLanguageModel.from_pretrained(
    model_name = "./Qwen3-32B-unsloth-bnb-4bit",  # 根据您的模型路径调整
    max_seq_length = max_seq_length,
    dtype = dtype,
    load_in_4bit = load_in_4bit,
)

# 2. 添加LoRA适配器
model = FastLanguageModel.get_peft_model(
    model,
    r = 16,
    target_modules = ["q_proj", "k_proj", "v_proj", "o_proj",
                      "gate_proj", "up_proj", "down_proj",],
    lora_alpha = 16,
    lora_dropout = 0,
    bias = "none",
    use_gradient_checkpointing = "unsloth",
    random_state = 3407,
    use_rslora = False,
    loftq_config = None,
)

# 3. 加载数据集
dataset = load_from_disk("./FineTome-10k-Unsloth")

# 4. 数据格式化函数
def formatting_prompts_func(examples):
    convos = examples["conversations"]
    texts = []
    for convo in convos:
        text = tokenizer.apply_chat_template(convo, tokenize = False, add_generation_prompt = False)
        texts.append(text)
    return { "text" : texts, }

dataset = dataset.map(formatting_prompts_func, batched = True,)

# 5. 训练配置
trainer = SFTTrainer(
    model = model,
    tokenizer = tokenizer,
    train_dataset = dataset,
    dataset_text_field = "text",
    max_seq_length = max_seq_length,
    dataset_num_proc = 2,
    packing = False,
    args = TrainingArguments(
        per_device_train_batch_size = 2,
        gradient_accumulation_steps = 4,
        warmup_steps = 5,
        max_steps = 100,  # 调整训练步数
        learning_rate = 2e-4,
        fp16 = not torch.cuda.is_bf16_supported(),
        bf16 = torch.cuda.is_bf16_supported(),
        logging_steps = 1,
        optim = "adamw_8bit",
        weight_decay = 0.01,
        lr_scheduler_type = "linear",
        seed = 3407,
        output_dir = "outputs",
    ),
)

# 6. 开始训练
trainer_stats = trainer.train()

# 7. 保存模型
model.save_pretrained("finetome_lora_model")
tokenizer.save_pretrained("finetome_lora_model")
'''
    
    with open('unsloth_training_template.py', 'w', encoding='utf-8') as f:
        f.write(script_content.strip())
    
    print("Unsloth训练脚本模板已保存到: unsloth_training_template.py")

if __name__ == "__main__":
    # 转换数据集
    dataset = convert_finetome_to_unsloth_format()
    
    # 创建训练脚本模板
    create_unsloth_training_script()
    
    print("\n✅ 数据集转换完成！")
    print("\n可用文件:")
    print("1. finetome-10k-unsloth.jsonl - JSONL格式数据")
    print("2. FineTome-10k-Unsloth/ - Arrow格式数据集")
    print("3. unsloth_training_template.py - 训练脚本模板")
    
    print("\n下一步建议:")
    print("1. 检查转换后的数据格式")
    print("2. 根据需要调整训练参数")
    print("3. 运行训练脚本开始微调")
    print("4. 如需处理完整的100k数据，请修改脚本中的数据量限制")
