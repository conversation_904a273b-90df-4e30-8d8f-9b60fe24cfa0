from datasets import load_dataset
import json
import os

def download_finetome_100k():
    """
    下载FineTome-100k数据集并转换为JSONL格式
    """
    print("开始下载FineTome-100k数据集...")
    
    try:
        # 下载数据集
        dataset = load_dataset("mlabonne/FineTome-100k")
        
        print(f"数据集下载完成！")
        print(f"- 训练集样本数: {len(dataset['train'])}")
        print(f"- 数据集字段: {dataset['train'].column_names}")
        
        # 查看数据集的前几个样本
        print("\n数据集样本预览:")
        for i, sample in enumerate(dataset['train'].select(range(3))):
            print(f"\n样本 {i+1}:")
            for key, value in sample.items():
                if isinstance(value, str) and len(value) > 200:
                    print(f"  {key}: {value[:200]}...")
                else:
                    print(f"  {key}: {value}")
        
        # 转换为JSONL格式
        output_file = 'finetome-100k.jsonl'
        print(f"\n正在转换为JSONL格式: {output_file}")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for i, item in enumerate(dataset['train']):
                json.dump(item, f, ensure_ascii=False)
                f.write('\n')
                
                # 每10000条显示进度
                if (i + 1) % 10000 == 0:
                    print(f"已处理 {i + 1} 条数据")
        
        print(f"\n转换完成！")
        print(f"- JSONL文件: {output_file}")
        print(f"- 文件大小: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
        print(f"- 保存位置: {os.path.abspath(output_file)}")
        
        # 保存数据集到本地Arrow格式（可选）
        print(f"\n正在保存数据集到本地Arrow格式...")
        dataset.save_to_disk("./FineTome-100k")
        print(f"Arrow格式数据集已保存到: ./FineTome-100k")
        
        return dataset
        
    except Exception as e:
        print(f"下载失败: {str(e)}")
        print("可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 确保已安装datasets库: pip install datasets")
        print("3. 如果是网络问题，可以尝试使用代理或稍后重试")
        return None

if __name__ == "__main__":
    dataset = download_finetome_100k()
    
    if dataset:
        print("\n✅ FineTome-100k数据集下载和转换完成！")
        print("\n数据集可用于:")
        print("- 指令微调训练")
        print("- 对话能力提升")
        print("- 多轮对话训练")
        print("\n建议下一步:")
        print("1. 检查数据格式是否符合您的训练需求")
        print("2. 根据需要进行数据预处理")
        print("3. 配置训练参数开始微调")
    else:
        print("\n❌ 数据集下载失败，请检查错误信息并重试")
