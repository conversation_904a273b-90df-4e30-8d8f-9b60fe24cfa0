import pandas as pd

def check_excel_file(filename):
    """检查Excel文件内容"""
    print(f"检查Excel文件: {filename}")
    print("=" * 50)
    
    try:
        # 读取主数据表
        df_main = pd.read_excel(filename, sheet_name='法律问答数据')
        print(f"主数据表:")
        print(f"- 行数: {len(df_main)}")
        print(f"- 列数: {len(df_main.columns)}")
        print(f"- 列名: {list(df_main.columns)}")
        
        # 显示前几行数据
        print(f"\n前3行数据预览:")
        for i in range(min(3, len(df_main))):
            row = df_main.iloc[i]
            print(f"\n第{i+1}行:")
            print(f"  序号: {row['序号']}")
            print(f"  用户问题: {row['用户问题'][:50]}...")
            print(f"  助手回答: {row['助手回答'][:50]}...")
            print(f"  问题长度: {row['问题长度']}")
            print(f"  回答长度: {row['回答长度']}")
        
        # 读取统计表
        df_stats = pd.read_excel(filename, sheet_name='数据统计')
        print(f"\n统计信息表:")
        print(df_stats.to_string(index=False))
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")

if __name__ == "__main__":
    check_excel_file("chinese_law_sample_3k.xlsx")
