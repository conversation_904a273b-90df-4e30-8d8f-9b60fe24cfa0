import json
import os

def remove_hongkong_entries(input_file, output_file):
    """
    删除JSONL文件中包含"香港"的数据条目
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
    """
    print(f"开始处理文件: {input_file}")
    
    total_count = 0
    removed_count = 0
    kept_data = []
    
    # 读取原文件
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            total_count += 1
            
            try:
                data = json.loads(line.strip())
                
                # 检查是否包含"香港"
                contains_hongkong = False
                
                # 检查所有字段中是否包含"香港"
                for key, value in data.items():
                    if isinstance(value, str) and "香港" in value:
                        contains_hongkong = True
                        break
                
                if contains_hongkong:
                    removed_count += 1
                    print(f"删除第 {line_num} 行数据 (包含'香港')")
                else:
                    kept_data.append(data)
                
                # 每1000行显示进度
                if total_count % 1000 == 0:
                    print(f"已处理 {total_count} 行，删除 {removed_count} 行")
                    
            except json.JSONDecodeError as e:
                print(f"第 {line_num} 行JSON解析错误: {e}")
                continue
    
    # 写入新文件
    print(f"正在保存到: {output_file}")
    with open(output_file, 'w', encoding='utf-8') as f:
        for data in kept_data:
            json.dump(data, f, ensure_ascii=False)
            f.write('\n')
    
    # 统计结果
    kept_count = len(kept_data)
    print(f"\n处理完成！")
    print(f"原始数据总数: {total_count}")
    print(f"删除数据条数: {removed_count}")
    print(f"保留数据条数: {kept_count}")
    print(f"删除比例: {removed_count/total_count*100:.2f}%")
    
    # 文件大小对比
    original_size = os.path.getsize(input_file) / (1024*1024)
    new_size = os.path.getsize(output_file) / (1024*1024)
    print(f"原文件大小: {original_size:.2f} MB")
    print(f"新文件大小: {new_size:.2f} MB")
    print(f"减少大小: {original_size - new_size:.2f} MB")
    
    return kept_count, removed_count

def preview_hongkong_entries(input_file, max_preview=5):
    """
    预览包含"香港"的数据条目
    """
    print(f"预览包含'香港'的数据条目 (最多显示{max_preview}条):")
    print("=" * 80)
    
    preview_count = 0
    total_hongkong = 0
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                
                # 检查是否包含"香港"
                contains_hongkong = False
                hongkong_fields = []
                
                for key, value in data.items():
                    if isinstance(value, str) and "香港" in value:
                        contains_hongkong = True
                        hongkong_fields.append(key)
                
                if contains_hongkong:
                    total_hongkong += 1
                    
                    if preview_count < max_preview:
                        print(f"第 {line_num} 行 (包含'香港'的字段: {', '.join(hongkong_fields)}):")
                        for key, value in data.items():
                            if key in hongkong_fields:
                                # 只显示包含香港的部分内容
                                if len(value) > 200:
                                    print(f"  {key}: {value[:200]}...")
                                else:
                                    print(f"  {key}: {value}")
                        print("-" * 40)
                        preview_count += 1
                        
            except json.JSONDecodeError:
                continue
    
    print(f"总共找到 {total_hongkong} 条包含'香港'的数据")
    return total_hongkong

if __name__ == "__main__":
    input_file = "hk-o1aw-sft-16_k-train copy.jsonl"
    output_file = "hk-o1aw-sft-16_k-train_no_hongkong.jsonl"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        exit(1)
    
    print("步骤1: 预览包含'香港'的数据")
    total_hongkong = preview_hongkong_entries(input_file)
    
    if total_hongkong == 0:
        print("没有找到包含'香港'的数据，无需处理。")
        exit(0)
    
    print(f"\n步骤2: 确认删除操作")
    print(f"将删除 {total_hongkong} 条包含'香港'的数据")
    
    # 执行删除操作
    print(f"\n步骤3: 开始删除操作")
    kept_count, removed_count = remove_hongkong_entries(input_file, output_file)
    
    print(f"\n✅ 处理完成！")
    print(f"新文件已保存为: {output_file}")
    print(f"建议检查新文件内容确认结果正确。")
