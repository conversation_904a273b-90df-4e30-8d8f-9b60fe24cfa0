import json
import re
from datasets import Dataset
import os

def extract_legal_references(text):
    """提取文本中的法律条文引用"""
    # 匹配各种法律条文格式
    patterns = [
        r'《([^》]+)》[第]*(\d+)条',  # 《法律名》第X条
        r'([^《》\s]+法)[第]*(\d+)条',  # XX法第X条
        r'([^《》\s]+条例)[第]*(\d+)条',  # XX条例第X条
        r'([^《》\s]+规定)[第]*(\d+)条',  # XX规定第X条
        r'([^《》\s]+规范)[第]*(\d+)条',  # XX规范第X条
    ]
    
    legal_refs = []
    for pattern in patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            if len(match) == 2:
                legal_refs.append(f"{match[0]}第{match[1]}条")
    
    return list(set(legal_refs))  # 去重

def analyze_question_type(question):
    """分析问题类型"""
    question_lower = question.lower()
    
    if any(keyword in question_lower for keyword in ['如何', '怎样', '怎么', '应该']):
        return "操作指导型"
    elif any(keyword in question_lower for keyword in ['是否', '能否', '可以', '符合']):
        return "判断分析型"
    elif any(keyword in question_lower for keyword in ['什么', '哪些', '包括']):
        return "概念解释型"
    elif any(keyword in question_lower for keyword in ['责任', '后果', '承担']):
        return "责任分析型"
    elif any(keyword in question_lower for keyword in ['程序', '流程', '步骤']):
        return "程序说明型"
    else:
        return "综合分析型"

def identify_legal_domain(question, answer):
    """识别法律领域"""
    text = question + " " + answer
    text_lower = text.lower()
    
    domains = []
    
    if any(keyword in text_lower for keyword in ['合伙', '企业', '公司', '股东', '董事']):
        domains.append("企业法")
    if any(keyword in text_lower for keyword in ['交通', '车辆', '驾驶', '道路']):
        domains.append("交通法")
    if any(keyword in text_lower for keyword in ['合同', '协议', '违约', '履行']):
        domains.append("合同法")
    if any(keyword in text_lower for keyword in ['破产', '清算', '债权', '债务']):
        domains.append("破产法")
    if any(keyword in text_lower for keyword in ['刑事', '犯罪', '刑罚', '判决']):
        domains.append("刑法")
    if any(keyword in text_lower for keyword in ['行政', '政府', '机关', '执法']):
        domains.append("行政法")
    if any(keyword in text_lower for keyword in ['民事', '侵权', '损害', '赔偿']):
        domains.append("民法")
    
    return domains if domains else ["一般法律"]

def generate_realistic_thinking(question, answer):
    """基于具体问题和答案生成真实的思考过程"""
    
    # 提取法律条文
    legal_refs = extract_legal_references(answer)
    
    # 分析问题类型
    question_type = analyze_question_type(question)
    
    # 识别法律领域
    legal_domains = identify_legal_domain(question, answer)
    
    # 提取关键事实
    key_facts = []
    if "某" in question:
        # 提取案例中的关键主体和行为
        subjects = re.findall(r'某([^，。；！？\s]+)', question)
        key_facts.extend([f"涉及主体：{s}" for s in subjects])
    
    # 提取行为描述
    if any(keyword in question for keyword in ['行为', '活动', '操作', '处理']):
        behaviors = re.findall(r'([^，。；！？]*(?:行为|活动|操作|处理)[^，。；！？]*)', question)
        key_facts.extend([f"关键行为：{b.strip()}" for b in behaviors if b.strip()])
    
    # 开始构建thinking过程
    thinking_parts = []
    
    # 1. 问题理解和事实梳理
    thinking_parts.append("**问题理解与事实梳理**")
    thinking_parts.append(f"这是一个{question_type}问题，主要涉及{'/'.join(legal_domains)}领域。")
    
    if key_facts:
        thinking_parts.append("关键事实包括：")
        for fact in key_facts[:3]:  # 限制数量
            thinking_parts.append(f"- {fact}")
    
    # 2. 法律适用分析
    if legal_refs:
        thinking_parts.append("\n**法律条文适用分析**")
        thinking_parts.append("根据相关法律规定，需要重点分析以下条文：")
        for ref in legal_refs[:3]:  # 限制数量
            thinking_parts.append(f"- {ref}")
        
        # 分析法条的适用条件
        if "第" in answer and "条" in answer:
            thinking_parts.append("\n这些条文的适用需要满足特定的构成要件，需要逐一对照案例事实进行分析。")
    
    # 3. 具体分析过程
    thinking_parts.append("\n**具体分析过程**")
    
    if question_type == "判断分析型":
        thinking_parts.append("对于这类判断性问题，需要：")
        thinking_parts.append("1. 明确判断标准（法律条文规定）")
        thinking_parts.append("2. 对照案例事实")
        thinking_parts.append("3. 得出是否符合的结论")
        
    elif question_type == "操作指导型":
        thinking_parts.append("对于这类操作指导问题，需要：")
        thinking_parts.append("1. 分析当前情况的法律性质")
        thinking_parts.append("2. 确定可采取的法律措施")
        thinking_parts.append("3. 评估各种措施的效果和风险")
        
    elif question_type == "责任分析型":
        thinking_parts.append("对于责任分析问题，需要：")
        thinking_parts.append("1. 确定责任主体")
        thinking_parts.append("2. 分析责任性质（民事/行政/刑事）")
        thinking_parts.append("3. 确定责任承担方式")
        
    elif question_type == "程序说明型":
        thinking_parts.append("对于程序性问题，需要：")
        thinking_parts.append("1. 梳理完整的程序流程")
        thinking_parts.append("2. 明确各环节的要求")
        thinking_parts.append("3. 注意程序的时限和条件")
    
    # 4. 风险和注意事项
    if any(keyword in answer for keyword in ['违反', '违法', '责任', '后果']):
        thinking_parts.append("\n**风险评估与注意事项**")
        if '违反' in answer or '违法' in answer:
            thinking_parts.append("- 需要注意违法行为可能面临的法律后果")
        if '责任' in answer:
            thinking_parts.append("- 需要明确责任承担的具体方式和范围")
        if '程序' in answer:
            thinking_parts.append("- 需要严格按照法定程序执行，避免程序性错误")
    
    # 5. 结论导向
    thinking_parts.append("\n**结论形成**")
    if legal_refs:
        thinking_parts.append(f"基于{legal_refs[0]}等相关规定，结合案例具体情况，可以得出明确的法律结论。")
    else:
        thinking_parts.append("基于相关法律原则和实践经验，结合案例具体情况，形成合理的法律建议。")
    
    return "\n".join(thinking_parts)

def enhance_dataset_with_realistic_thinking(input_file, output_file):
    """用真实的thinking过程增强数据集"""
    print(f"开始为 {input_file} 生成真实的thinking过程...")
    
    enhanced_data = []
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                conversations = data['conversations']
                
                # 获取用户问题和助手回答
                user_content = conversations[0]['content']
                assistant_content = conversations[1]['content']
                
                # 如果已经包含thinking，提取原始回答
                if '<think>' in assistant_content:
                    # 提取</think>后的内容作为原始回答
                    think_end = assistant_content.find('</think>')
                    if think_end != -1:
                        original_answer = assistant_content[think_end + 8:].strip()
                    else:
                        original_answer = assistant_content
                else:
                    original_answer = assistant_content
                
                # 生成真实的thinking过程
                realistic_thinking = generate_realistic_thinking(user_content, original_answer)
                
                # 构建新的助手回答
                enhanced_assistant_content = f"<think>\n{realistic_thinking}\n</think>\n\n{original_answer}"
                
                # 创建新的对话数据
                enhanced_conversation = {
                    "conversations": [
                        {"role": "user", "content": user_content},
                        {"role": "assistant", "content": enhanced_assistant_content}
                    ]
                }
                
                enhanced_data.append(enhanced_conversation)
                
                if line_num % 500 == 0:
                    print(f"已处理 {line_num} 条数据")
                    
            except json.JSONDecodeError as e:
                print(f"第 {line_num} 行JSON解析错误: {e}")
                continue
    
    # 保存增强后的数据
    print(f"正在保存到 {output_file}...")
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in enhanced_data:
            json.dump(item, f, ensure_ascii=False)
            f.write('\n')
    
    print(f"真实thinking生成完成！")
    print(f"- 处理数据条数: {len(enhanced_data)}")
    print(f"- 输出文件: {output_file}")
    print(f"- 文件大小: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
    
    return enhanced_data

def preview_realistic_thinking(enhanced_data, num_samples=2):
    """预览真实thinking数据"""
    print(f"\n真实thinking数据预览 (显示前{num_samples}条):")
    print("=" * 80)
    
    for i, sample in enumerate(enhanced_data[:num_samples]):
        print(f"\n样本 {i+1}:")
        user_content = sample['conversations'][0]['content']
        assistant_content = sample['conversations'][1]['content']
        
        print(f"用户问题: {user_content[:100]}...")
        
        # 提取thinking部分
        think_start = assistant_content.find('<think>')
        think_end = assistant_content.find('</think>')
        if think_start != -1 and think_end != -1:
            thinking_content = assistant_content[think_start+7:think_end]
            print(f"思考过程: {thinking_content[:300]}...")
        
        # 提取回答部分
        answer_start = think_end + 8 if think_end != -1 else 0
        answer_content = assistant_content[answer_start:].strip()
        print(f"最终回答: {answer_content[:200]}...")
        print("-" * 40)

def main():
    input_file = "chinese_law_sample_3k_with_thinking.jsonl"
    output_file = "chinese_law_sample_3k_realistic_thinking.jsonl"
    
    print("中国法律3K数据集真实Thinking生成工具")
    print("=" * 50)
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        return
    
    # 生成真实thinking
    enhanced_data = enhance_dataset_with_realistic_thinking(input_file, output_file)
    
    # 预览数据
    preview_realistic_thinking(enhanced_data)
    
    # 创建Arrow格式数据集
    dataset = Dataset.from_list(enhanced_data)
    dataset_dir = "./Chinese-Law-Sample-3K-Realistic-Thinking"
    dataset.save_to_disk(dataset_dir)
    print(f"Arrow格式数据集已保存到: {dataset_dir}")
    
    print(f"\n✅ 真实Thinking生成完成！")
    print(f"\n生成的文件:")
    print(f"1. {output_file} - 包含真实thinking的JSONL数据")
    print(f"2. {dataset_dir}/ - Arrow格式数据集")
    
    print(f"\n真实Thinking特点:")
    print(f"- 基于具体法律条文和问题内容生成")
    print(f"- 包含法律条文提取和分析")
    print(f"- 体现真实的法律推理过程")
    print(f"- 针对不同问题类型定制分析框架")

if __name__ == "__main__":
    main()
