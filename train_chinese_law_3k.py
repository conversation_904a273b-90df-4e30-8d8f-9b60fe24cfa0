#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国法律3K数据集Unsloth微调脚本
使用数据集: ./Chinese-Law-Sample-3K-Unsloth
"""

from unsloth import FastLanguageModel
from datasets import load_from_disk
from trl import SFTTrainer
from transformers import TrainingArguments
import torch
import os

# 设置环境变量
os.environ["CUDA_VISIBLE_DEVICES"] = "1"  # 根据您的GPU设置调整

def main():
    print("开始中国法律3K数据集微调...")
    
    # 1. 模型配置
    max_seq_length = 2048
    dtype = None
    load_in_4bit = True
    
    # 2. 加载模型
    print("加载模型...")
    model, tokenizer = FastLanguageModel.from_pretrained(
        model_name = "./Qwen3-32B-unsloth-bnb-4bit",
        max_seq_length = max_seq_length,
        dtype = dtype,
        load_in_4bit = load_in_4bit,
    )
    
    # 3. 添加LoRA适配器
    print("添加LoRA适配器...")
    model = FastLanguageModel.get_peft_model(
        model,
        r = 16,
        target_modules = ["q_proj", "k_proj", "v_proj", "o_proj",
                          "gate_proj", "up_proj", "down_proj",],
        lora_alpha = 16,
        lora_dropout = 0,
        bias = "none",
        use_gradient_checkpointing = "unsloth",
        random_state = 3407,
        use_rslora = False,
        loftq_config = None,
    )
    
    # 4. 加载数据集
    print("加载数据集...")
    dataset = load_from_disk("./Chinese-Law-Sample-3K-Unsloth")
    print(f"数据集大小: {len(dataset)}")
    
    # 5. 数据格式化函数
    def formatting_prompts_func(examples):
        convos = examples["conversations"]
        texts = []
        for convo in convos:
            text = tokenizer.apply_chat_template(
                convo, 
                tokenize = False, 
                add_generation_prompt = False
            )
            texts.append(text)
        return {"text": texts}
    
    print("格式化数据集...")
    dataset = dataset.map(formatting_prompts_func, batched=True)
    
    # 6. 训练配置
    print("配置训练参数...")
    trainer = SFTTrainer(
        model = model,
        tokenizer = tokenizer,
        train_dataset = dataset,
        dataset_text_field = "text",
        max_seq_length = max_seq_length,
        dataset_num_proc = 2,
        packing = False,
        args = TrainingArguments(
            per_device_train_batch_size = 2,
            gradient_accumulation_steps = 4,
            warmup_steps = 10,
            max_steps = 300,  # 3K数据集适合的训练步数
            learning_rate = 2e-4,
            fp16 = not torch.cuda.is_bf16_supported(),
            bf16 = torch.cuda.is_bf16_supported(),
            logging_steps = 10,
            optim = "adamw_8bit",
            weight_decay = 0.01,
            lr_scheduler_type = "linear",
            seed = 3407,
            output_dir = "outputs",
            save_steps = 50,
            save_total_limit = 3,
            dataloader_num_workers = 2,
            evaluation_strategy = "no",
            report_to = "none",
        ),
    )
    
    # 7. 显示GPU内存使用情况
    gpu_stats = torch.cuda.get_device_properties(0)
    start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)
    max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)
    print(f"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.")
    print(f"{start_gpu_memory} GB of memory reserved.")
    
    # 8. 开始训练
    print("开始训练...")
    trainer_stats = trainer.train()
    
    # 9. 显示训练后的内存使用情况
    used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)
    used_memory_for_lora = round(used_memory - start_gpu_memory, 3)
    used_percentage = round(used_memory / max_memory * 100, 3)
    lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)
    print(f"{used_memory} GB of memory reserved.")
    print(f"{used_memory_for_lora} GB used for LoRA ({lora_percentage}%).")
    print(f"{used_percentage}% of total memory used.")
    
    # 10. 保存模型
    print("保存模型...")
    model.save_pretrained("chinese_law_3k_model")
    tokenizer.save_pretrained("chinese_law_3k_model")
    
    print(f"训练完成！模型已保存到: chinese_law_3k_model")
    
    # 11. 测试模型
    print("\n测试微调后的模型...")
    FastLanguageModel.for_inference(model)
    
    # 测试法律问题
    test_questions = [
        "什么是合同法中的要约？",
        "交通事故中如何确定责任？",
        "企业破产清算的基本程序是什么？"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n测试问题 {i}: {question}")
        
        messages = [
            {"role": "user", "content": question}
        ]
        
        inputs = tokenizer.apply_chat_template(
            messages,
            tokenize = True,
            add_generation_prompt = True,
            return_tensors = "pt"
        ).to("cuda")
        
        outputs = model.generate(
            input_ids = inputs,
            max_new_tokens = 256,
            use_cache = True,
            temperature = 0.7,
            top_p = 0.9,
            do_sample = True,
        )
        
        response = tokenizer.batch_decode(outputs)
        print("模型回复:")
        # 只显示生成的部分，去掉输入
        generated_text = response[0][len(tokenizer.decode(inputs[0])):]
        print(generated_text)
        print("-" * 50)

if __name__ == "__main__":
    main()
