# FineTome-100k 数据集下载和使用指南

## 📋 概述

本项目成功下载并转换了 FineTome-100k 数据集，这是一个高质量的指令微调数据集，包含100,000条对话数据，适用于大语言模型的指令跟随能力训练。

## 📁 文件结构

```
├── 原始数据集
│   ├── finetome-100k.jsonl              # 原始JSONL格式 (259.52 MB)
│   └── FineTome-100k/                   # 原始Arrow格式数据集
│
├── 转换后的数据集
│   ├── finetome-10k-unsloth.jsonl       # 10k样本Unsloth格式 (24.25 MB)
│   ├── FineTome-10k-Unsloth/            # 10k样本Arrow格式
│   ├── finetome-50k-unsloth.jsonl       # 50k样本Unsloth格式 (123.07 MB)
│   └── FineTome-50k-Unsloth/            # 50k样本Arrow格式
│
├── 训练脚本
│   ├── unsloth_training_template.py     # 基础训练模板
│   └── train_finetome_50k_model.py      # 50k数据集训练脚本
│
└── 工具脚本
    ├── download_finetome.py             # 数据集下载脚本
    ├── convert_finetome_for_unsloth.py  # 格式转换脚本
    └── process_full_finetome.py         # 完整处理脚本
```

## 🔄 数据格式转换

### 原始格式 (FineTome)
```json
{
  "conversations": [
    {"from": "human", "value": "用户问题"},
    {"from": "gpt", "value": "助手回答"}
  ],
  "source": "数据来源",
  "score": 5.212620735168457
}
```

### Unsloth格式
```json
{
  "conversations": [
    {"role": "user", "content": "用户问题"},
    {"role": "assistant", "content": "助手回答"}
  ]
}
```

## 🚀 使用方法

### 1. 下载完整数据集
```bash
python download_finetome.py
```

### 2. 转换为Unsloth格式
```bash
# 处理前10k样本
python convert_finetome_for_unsloth.py

# 处理指定数量的样本
python process_full_finetome.py --max_samples 50000 --model_name my_model

# 处理全部100k样本
python process_full_finetome.py --model_name full_finetome_model
```

### 3. 开始训练
```bash
# 使用生成的训练脚本
python train_finetome_50k_model.py
```

## ⚙️ 训练配置

### 推荐配置
- **模型**: Qwen3-32B (4bit量化)
- **LoRA参数**: r=16, alpha=16
- **批次大小**: 2 (per_device_train_batch_size)
- **梯度累积**: 4步
- **学习率**: 2e-4
- **最大序列长度**: 2048
- **训练步数**: 500 (可根据数据集大小调整)

### GPU要求
- **推荐**: NVIDIA H800/A100 (80GB显存)
- **最低**: RTX 4090 (24GB显存)
- **显存使用**: 约38GB (4bit量化模式)

## 📊 数据集统计

| 数据集版本 | 样本数量 | 文件大小 | 训练时间估计 |
|-----------|---------|----------|-------------|
| 10k       | 10,000  | 24.25 MB | ~1小时      |
| 50k       | 50,000  | 123.07 MB| ~5小时      |
| 100k      | 100,000 | 259.52 MB| ~10小时     |

## 🎯 数据集特点

### 优势
- **高质量**: 经过精心筛选和评分的对话数据
- **多样性**: 涵盖编程、数学、科学、常识等多个领域
- **结构化**: 标准的用户-助手对话格式
- **可扩展**: 支持多轮对话和复杂指令

### 适用场景
- 指令跟随能力训练
- 对话能力提升
- 代码生成能力增强
- 推理能力改进

## 🔧 自定义配置

### 修改训练参数
编辑训练脚本中的以下参数：
```python
# 训练步数
max_steps = 500

# 批次大小
per_device_train_batch_size = 2

# 学习率
learning_rate = 2e-4

# LoRA参数
r = 16
lora_alpha = 16
```

### 调整数据集大小
```bash
# 自定义样本数量
python process_full_finetome.py --max_samples 20000 --model_name custom_model
```

## 📈 训练监控

训练过程中会显示：
- GPU内存使用情况
- 训练损失变化
- 训练进度和剩余时间
- 模型保存状态

## 🎉 训练完成后

训练完成后会生成：
- **模型文件**: `{model_name}/` 目录
- **LoRA权重**: 可与原模型合并
- **分词器**: 配套的tokenizer文件
- **测试输出**: 自动测试模型回复质量

## 🔍 质量评估

建议训练完成后进行以下测试：
1. **指令跟随**: 测试复杂指令的执行能力
2. **对话连贯性**: 检查多轮对话的逻辑性
3. **专业知识**: 验证特定领域的回答质量
4. **代码生成**: 测试编程相关任务

## 🚨 注意事项

1. **显存要求**: 确保GPU显存足够（推荐80GB+）
2. **训练时间**: 大数据集训练时间较长，建议使用screen或tmux
3. **模型保存**: 定期保存检查点，避免训练中断丢失进度
4. **数据质量**: 可根据需要过滤低质量样本

## 📞 技术支持

如遇到问题，请检查：
- GPU驱动和CUDA版本
- Python依赖包版本
- 数据集文件完整性
- 训练参数配置

---

**祝您训练顺利！🎯**
