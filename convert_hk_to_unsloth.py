import json
from datasets import Dataset
import os

def convert_hk_data_to_unsloth_format(input_file, output_file, include_thinking=True):
    """
    将HK数据集转换为适合Unsloth训练的格式
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
        include_thinking: 是否在回答中包含思考过程
    """
    print(f"开始转换 {input_file} 为Unsloth格式...")
    print(f"包含思考过程: {include_thinking}")
    
    conversations_data = []
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                
                # 提取字段
                prompt = data.get('prompt', '')
                thinking = data.get('thinking', '')
                answer = data.get('answer', '')
                
                # 构建助手回答
                if include_thinking and thinking.strip():
                    # 包含思考过程的完整回答
                    assistant_content = f"<think>\n{thinking.strip()}\n</think>\n\n{answer}"
                else:
                    # 只包含最终答案
                    assistant_content = answer
                
                # 转换为Unsloth对话格式
                conversation = {
                    "conversations": [
                        {
                            "role": "user",
                            "content": prompt
                        },
                        {
                            "role": "assistant", 
                            "content": assistant_content
                        }
                    ]
                }
                
                conversations_data.append(conversation)
                
                if line_num % 1000 == 0:
                    print(f"已处理 {line_num} 条数据")
                    
            except json.JSONDecodeError as e:
                print(f"第 {line_num} 行JSON解析错误: {e}")
                continue
    
    # 保存为JSONL格式
    print(f"正在保存到: {output_file}")
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in conversations_data:
            json.dump(item, f, ensure_ascii=False)
            f.write('\n')
    
    print(f"转换完成！")
    print(f"- 原始数据条数: {line_num}")
    print(f"- 转换后数据条数: {len(conversations_data)}")
    print(f"- 输出文件: {output_file}")
    print(f"- 文件大小: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
    
    return conversations_data

def create_dataset_and_training_script(conversations_data, dataset_name, model_output_name):
    """
    创建Dataset并生成训练脚本
    """
    # 创建Dataset
    dataset = Dataset.from_list(conversations_data)
    dataset_dir = f"./{dataset_name}"
    dataset.save_to_disk(dataset_dir)
    print(f"Arrow格式数据集已保存到: {dataset_dir}")
    
    # 生成训练脚本
    script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HK法律数据集Unsloth微调脚本
使用数据集: {dataset_dir}
"""

from unsloth import FastLanguageModel
from datasets import load_from_disk
from trl import SFTTrainer
from transformers import TrainingArguments
import torch
import os

# 设置环境变量
os.environ["CUDA_VISIBLE_DEVICES"] = "1"  # 根据您的GPU设置调整

def main():
    print("开始HK法律数据集微调...")
    
    # 1. 模型配置
    max_seq_length = 2048
    dtype = None
    load_in_4bit = True
    
    # 2. 加载模型
    print("加载模型...")
    model, tokenizer = FastLanguageModel.from_pretrained(
        model_name = "./Qwen3-32B-unsloth-bnb-4bit",
        max_seq_length = max_seq_length,
        dtype = dtype,
        load_in_4bit = load_in_4bit,
    )
    
    # 3. 添加LoRA适配器
    print("添加LoRA适配器...")
    model = FastLanguageModel.get_peft_model(
        model,
        r = 16,
        target_modules = ["q_proj", "k_proj", "v_proj", "o_proj",
                          "gate_proj", "up_proj", "down_proj",],
        lora_alpha = 16,
        lora_dropout = 0,
        bias = "none",
        use_gradient_checkpointing = "unsloth",
        random_state = 3407,
        use_rslora = False,
        loftq_config = None,
    )
    
    # 4. 加载数据集
    print("加载数据集...")
    dataset = load_from_disk("{dataset_dir}")
    print(f"数据集大小: {{len(dataset)}}")
    
    # 5. 数据格式化函数
    def formatting_prompts_func(examples):
        convos = examples["conversations"]
        texts = []
        for convo in convos:
            text = tokenizer.apply_chat_template(
                convo, 
                tokenize = False, 
                add_generation_prompt = False,
                enable_thinking = True  # 启用思考模式
            )
            texts.append(text)
        return {{"text": texts}}
    
    print("格式化数据集...")
    dataset = dataset.map(formatting_prompts_func, batched=True)
    
    # 6. 训练配置
    print("配置训练参数...")
    trainer = SFTTrainer(
        model = model,
        tokenizer = tokenizer,
        train_dataset = dataset,
        dataset_text_field = "text",
        max_seq_length = max_seq_length,
        dataset_num_proc = 2,
        packing = False,
        args = TrainingArguments(
            per_device_train_batch_size = 2,
            gradient_accumulation_steps = 4,
            warmup_steps = 10,
            max_steps = 1000,  # 根据数据集大小调整
            learning_rate = 2e-4,
            fp16 = not torch.cuda.is_bf16_supported(),
            bf16 = torch.cuda.is_bf16_supported(),
            logging_steps = 10,
            optim = "adamw_8bit",
            weight_decay = 0.01,
            lr_scheduler_type = "linear",
            seed = 3407,
            output_dir = "outputs",
            save_steps = 200,
            save_total_limit = 3,
            dataloader_num_workers = 2,
        ),
    )
    
    # 7. 显示GPU内存使用情况
    gpu_stats = torch.cuda.get_device_properties(0)
    start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)
    max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)
    print(f"GPU = {{gpu_stats.name}}. Max memory = {{max_memory}} GB.")
    print(f"{{start_gpu_memory}} GB of memory reserved.")
    
    # 8. 开始训练
    print("开始训练...")
    trainer_stats = trainer.train()
    
    # 9. 显示训练后的内存使用情况
    used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)
    used_memory_for_lora = round(used_memory - start_gpu_memory, 3)
    used_percentage = round(used_memory / max_memory * 100, 3)
    lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)
    print(f"{{used_memory}} GB of memory reserved.")
    print(f"{{used_memory_for_lora}} GB used for LoRA ({{lora_percentage}}%).")
    print(f"{{used_percentage}}% of total memory used.")
    
    # 10. 保存模型
    print("保存模型...")
    model.save_pretrained("{model_output_name}")
    tokenizer.save_pretrained("{model_output_name}")
    
    print(f"训练完成！模型已保存到: {model_output_name}")
    
    # 11. 测试模型
    print("\\n测试微调后的模型...")
    FastLanguageModel.for_inference(model)
    
    messages = [
        {{"role": "user", "content": "请解释什么是合同法中的要约？"}}
    ]
    
    inputs = tokenizer.apply_chat_template(
        messages,
        tokenize = True,
        add_generation_prompt = True,
        enable_thinking = True,
        return_tensors = "pt"
    ).to("cuda")
    
    outputs = model.generate(
        input_ids = inputs,
        max_new_tokens = 512,
        use_cache = True,
        temperature = 0.7,
        top_p = 0.9,
    )
    
    response = tokenizer.batch_decode(outputs)
    print("模型回复:")
    print(response[0])

if __name__ == "__main__":
    main()
'''
    
    script_filename = f'train_{model_output_name}.py'
    with open(script_filename, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"训练脚本已保存到: {script_filename}")
    return script_filename

def preview_converted_data(conversations_data, num_samples=3):
    """
    预览转换后的数据格式
    """
    print(f"\\n转换后数据格式预览 (显示前{num_samples}条):")
    print("=" * 80)
    
    for i, sample in enumerate(conversations_data[:num_samples]):
        print(f"\\n样本 {i+1}:")
        for conv in sample['conversations']:
            role = conv['role']
            content = conv['content']
            if len(content) > 300:
                content = content[:300] + "..."
            print(f"  {role}: {content}")
        print("-" * 40)

def main():
    input_file = "hk-o1aw-sft-16_k-train_no_hongkong.jsonl"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        return
    
    print("HK法律数据集转换为Unsloth格式")
    print("=" * 50)
    
    # 提供两种转换选项
    print("转换选项:")
    print("1. 包含思考过程 (推荐用于训练思维链能力)")
    print("2. 仅包含最终答案 (更简洁的回答)")
    
    choice = input("请选择转换方式 (1/2, 默认1): ").strip()
    include_thinking = choice != "2"
    
    # 设置输出文件名
    if include_thinking:
        output_file = "hk_legal_unsloth_with_thinking.jsonl"
        dataset_name = "HK-Legal-Unsloth-Thinking"
        model_name = "hk_legal_thinking_model"
    else:
        output_file = "hk_legal_unsloth_simple.jsonl"
        dataset_name = "HK-Legal-Unsloth-Simple"
        model_name = "hk_legal_simple_model"
    
    # 执行转换
    conversations_data = convert_hk_data_to_unsloth_format(
        input_file, output_file, include_thinking
    )
    
    # 预览数据
    preview_converted_data(conversations_data)
    
    # 创建数据集和训练脚本
    script_file = create_dataset_and_training_script(
        conversations_data, dataset_name, model_name
    )
    
    print(f"\\n✅ 转换完成！")
    print(f"\\n生成的文件:")
    print(f"1. {output_file} - JSONL格式数据")
    print(f"2. {dataset_name}/ - Arrow格式数据集")
    print(f"3. {script_file} - 训练脚本")
    
    print(f"\\n使用方法:")
    print(f"python {script_file}")
    
    print(f"\\n数据集特点:")
    if include_thinking:
        print("- 包含完整的思考过程，有助于训练模型的推理能力")
        print("- 使用<think></think>标签包装思考内容")
        print("- 适合训练具有思维链能力的法律助手")
    else:
        print("- 仅包含最终答案，更加简洁")
        print("- 适合训练快速响应的法律问答模型")

if __name__ == "__main__":
    main()
