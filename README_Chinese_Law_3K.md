# 中国法律3K数据集抽取和转换指南

## 📋 概述

本项目成功从`chinese_law_ft_train.jsonl`（包含281,142条数据）中随机抽取了3,000条高质量的中国法律数据，并转换为适合Unsloth微调Qwen3的格式。

## 📊 数据集统计

- **原始数据总数**: 281,142条
- **抽取数据数量**: 3,000条
- **抽取比例**: 1.07%
- **随机种子**: 42 (确保结果可重现)

## 📁 文件结构

```
├── 原始数据
│   └── chinese_law_ft_train.jsonl                  # 原始完整数据集
│
├── 抽取的数据集
│   ├── chinese_law_sample_3k.jsonl                 # 原格式抽取数据 (3.69 MB)
│   ├── chinese_law_sample_3k_unsloth.jsonl         # Unsloth格式数据 (3.78 MB)
│   └── Chinese-Law-Sample-3K-Unsloth/              # Arrow格式数据集
│
├── 训练脚本
│   └── train_chinese_law_3k.py                     # 3K数据集训练脚本
│
└── 工具脚本
    └── sample_chinese_law_data.py                   # 数据抽取脚本
```

## 🔄 数据格式转换

### 原始格式
```json
{
  "instruction": "法律问题描述",
  "input": "补充输入信息（可能为空）",
  "output": "法律专业回答",
  "id": 58370
}
```

### Unsloth格式
```json
{
  "conversations": [
    {"role": "user", "content": "法律问题描述\n\n补充输入信息"},
    {"role": "assistant", "content": "法律专业回答"}
  ]
}
```

## 📈 数据质量分析

### 数据特点
- **专业性强**: 涵盖合伙企业法、交通法规、企业破产法等多个法律领域
- **结构完整**: 每条数据都包含完整的问题和专业回答
- **语言规范**: 使用标准的法律术语和表达方式
- **实用性高**: 包含实际案例分析和法条引用

### 覆盖领域
- 合伙企业法
- 交通法规
- 企业破产法
- 民商法
- 行政法规
- 执法规范

## 🚀 使用方法

### 1. 直接使用训练脚本
```bash
python train_chinese_law_3k.py
```

### 2. 自定义训练
```python
from datasets import load_from_disk

# 加载数据集
dataset = load_from_disk("./Chinese-Law-Sample-3K-Unsloth")
print(f"数据集大小: {len(dataset)}")  # 3000
```

## ⚙️ 训练配置

### 推荐参数
- **模型**: Qwen3-32B (4bit量化)
- **数据量**: 3,000条
- **训练步数**: 300步 (适合3K数据集)
- **批次大小**: 2 (per_device_train_batch_size)
- **梯度累积**: 4步
- **学习率**: 2e-4
- **保存频率**: 每50步保存一次

### 训练时间估算
- **GPU**: H800/A100 (80GB) - 约30分钟
- **GPU**: RTX 4090 (24GB) - 约45分钟
- **总步数**: 300步
- **有效批次大小**: 8 (2 × 4)

## 🎯 数据集优势

### 1. 适中的数据量
- 3K数据量适合快速实验和原型开发
- 训练时间短，便于参数调优
- 资源消耗相对较少

### 2. 高质量内容
- 从28万条数据中精选
- 随机抽取保证代表性
- 专业法律知识覆盖全面

### 3. 标准化格式
- 完全兼容Unsloth框架
- 标准的对话格式
- 即插即用的训练数据

## 📊 性能预期

### 训练效果
- **收敛速度**: 快速收敛（约200-300步）
- **过拟合风险**: 较低（数据量适中）
- **泛化能力**: 良好（涵盖多个法律领域）

### 应用场景
- 法律咨询原型系统
- 法律知识问答
- 法律文档分析
- 合规检查助手

## 🔧 自定义抽取

如需重新抽取或调整数量，可以修改脚本参数：

```python
# 修改sample_chinese_law_data.py中的参数
sample_size = 5000  # 调整抽取数量
seed = 123         # 修改随机种子
```

## 📝 数据样本示例

### 样本1: 合伙企业法
**问题**: 某合伙企业在清算期间，其中一个合伙人私自开展了与清算无关的经营活动，违反了合伙企业法的相关规定。其他合伙人该如何应对？

**回答**: 根据合伙企业法第三十三条第一款的规定，合伙企业在清算期间不得开展与清算无关的经营活动...

### 样本2: 交通执法
**问题**: 某交通警察在路面执勤时，对一辆违规停车的车辆进行扣留，并要求开具罚款收据，并填写了大于实际罚款金额的数额。这个行为是否符合交通警察道路执勤执法工作规范中的相关规定？

**回答**: 该交通警察的行为违反了交通警察道路执勤执法工作规范中的第八十四条第一款和第二款...

## 🚨 注意事项

1. **数据随机性**: 使用固定随机种子(42)确保结果可重现
2. **训练步数**: 3K数据建议300步，避免过拟合
3. **评估方法**: 建议使用法律专业问题进行人工评估
4. **扩展性**: 可与其他法律数据集合并使用

## 📈 后续扩展

### 数据增强
- 可以抽取更多数据（5K、10K等）
- 结合其他法律数据集
- 添加特定领域的法律数据

### 模型优化
- 调整LoRA参数
- 尝试不同的学习率
- 实验不同的训练策略

---

**祝您训练顺利！⚖️**
