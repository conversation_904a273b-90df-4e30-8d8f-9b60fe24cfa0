from datasets import load_dataset
import json
import os

def download_chinese_law_dataset():
    """
    下载Kuugo/chinese_law_ft_dataset数据集并转换为JSONL格式
    """
    print("开始下载Kuugo/chinese_law_ft_dataset数据集...")
    
    try:
        # 下载数据集
        dataset = load_dataset("Kuugo/chinese_law_ft_dataset")
        
        print(f"数据集下载完成！")
        print(f"数据集信息:")
        for split_name, split_data in dataset.items():
            print(f"- {split_name}: {len(split_data)} 条数据")
        
        # 获取第一个分割的数据来查看结构
        first_split = list(dataset.keys())[0]
        first_data = dataset[first_split]
        print(f"- 数据集字段: {first_data.column_names}")
        
        # 查看数据集的前几个样本
        print("\n数据集样本预览:")
        for i, sample in enumerate(first_data.select(range(min(3, len(first_data))))):
            print(f"\n样本 {i+1}:")
            for key, value in sample.items():
                if isinstance(value, str) and len(value) > 200:
                    print(f"  {key}: {value[:200]}...")
                else:
                    print(f"  {key}: {value}")
        
        # 转换每个分割为JSONL格式
        for split_name, split_data in dataset.items():
            output_file = f'chinese_law_ft_{split_name}.jsonl'
            print(f"\n正在转换 {split_name} 为JSONL格式: {output_file}")
            
            with open(output_file, 'w', encoding='utf-8') as f:
                for i, item in enumerate(split_data):
                    json.dump(item, f, ensure_ascii=False)
                    f.write('\n')
                    
                    # 每5000条显示进度
                    if (i + 1) % 5000 == 0:
                        print(f"已处理 {split_name} {i + 1} 条数据")
            
            print(f"{split_name} 转换完成！文件大小: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
        
        # 保存数据集到本地Arrow格式
        print(f"\n正在保存数据集到本地Arrow格式...")
        dataset.save_to_disk("./chinese_law_ft_dataset")
        print(f"Arrow格式数据集已保存到: ./chinese_law_ft_dataset")
        
        # 统计总数据量
        total_samples = sum(len(split_data) for split_data in dataset.values())
        print(f"\n数据集统计:")
        print(f"- 总样本数: {total_samples}")
        print(f"- 分割数量: {len(dataset)}")
        
        return dataset
        
    except Exception as e:
        print(f"下载失败: {str(e)}")
        print("可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 确保已安装datasets库: pip install datasets")
        print("3. 检查HuggingFace Hub访问权限")
        print("4. 如果是网络问题，可以尝试使用代理或稍后重试")
        return None

def convert_to_unsloth_format(dataset, output_prefix="chinese_law_unsloth"):
    """
    将数据集转换为Unsloth格式
    """
    print(f"\n开始转换为Unsloth格式...")
    
    for split_name, split_data in dataset.items():
        print(f"\n处理 {split_name} 分割...")
        conversations_data = []
        
        for i, item in enumerate(split_data):
            # 根据数据集的实际字段结构进行转换
            # 这里需要根据实际数据结构调整
            if 'instruction' in item and 'output' in item:
                # 标准指令格式
                conversation = {
                    "conversations": [
                        {"role": "user", "content": item['instruction']},
                        {"role": "assistant", "content": item['output']}
                    ]
                }
            elif 'input' in item and 'output' in item:
                # 输入输出格式
                conversation = {
                    "conversations": [
                        {"role": "user", "content": item['input']},
                        {"role": "assistant", "content": item['output']}
                    ]
                }
            elif 'question' in item and 'answer' in item:
                # 问答格式
                conversation = {
                    "conversations": [
                        {"role": "user", "content": item['question']},
                        {"role": "assistant", "content": item['answer']}
                    ]
                }
            else:
                # 尝试自动检测字段
                keys = list(item.keys())
                if len(keys) >= 2:
                    # 假设第一个字段是问题，第二个是答案
                    conversation = {
                        "conversations": [
                            {"role": "user", "content": str(item[keys[0]])},
                            {"role": "assistant", "content": str(item[keys[1]])}
                        ]
                    }
                else:
                    print(f"警告: 无法识别第 {i+1} 条数据的格式，跳过")
                    continue
            
            conversations_data.append(conversation)
            
            if (i + 1) % 1000 == 0:
                print(f"已处理 {i + 1} 条数据")
        
        # 保存Unsloth格式
        output_file = f'{output_prefix}_{split_name}.jsonl'
        with open(output_file, 'w', encoding='utf-8') as f:
            for item in conversations_data:
                json.dump(item, f, ensure_ascii=False)
                f.write('\n')
        
        print(f"{split_name} Unsloth格式转换完成！")
        print(f"- 原始数据: {len(split_data)} 条")
        print(f"- 转换后数据: {len(conversations_data)} 条")
        print(f"- 输出文件: {output_file}")
        print(f"- 文件大小: {os.path.getsize(output_file) / (1024*1024):.2f} MB")

if __name__ == "__main__":
    # 下载数据集
    dataset = download_chinese_law_dataset()
    
    if dataset:
        print("\n✅ 中国法律数据集下载完成！")
        
        # 询问是否转换为Unsloth格式
        convert_choice = input("\n是否转换为Unsloth格式？(y/n, 默认y): ").strip().lower()
        if convert_choice != 'n':
            convert_to_unsloth_format(dataset)
            print("\n✅ Unsloth格式转换完成！")
        
        print("\n数据集可用于:")
        print("- 中文法律问答训练")
        print("- 法律知识微调")
        print("- 法律咨询系统开发")
        print("\n建议下一步:")
        print("1. 检查数据格式是否符合您的训练需求")
        print("2. 根据需要进行数据预处理")
        print("3. 配置训练参数开始微调")
    else:
        print("\n❌ 数据集下载失败，请检查错误信息并重试")
