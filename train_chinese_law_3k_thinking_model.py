#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国法律3K数据集(含Thinking)Unsloth微调脚本
使用数据集: ./Chinese-Law-Sample-3K-Thinking
"""

from unsloth import FastLanguageModel
from datasets import load_from_disk
from trl import SFTTrainer
from transformers import TrainingArguments
import torch
import os

def main():
    print("开始中国法律3K数据集(含Thinking)微调...")
    
    # 1. 模型配置
    max_seq_length = 2048
    dtype = None
    load_in_4bit = True
    
    # 2. 加载模型
    print("加载模型...")
    model, tokenizer = FastLanguageModel.from_pretrained(
        model_name = "./Qwen3-32B-unsloth-bnb-4bit",
        max_seq_length = max_seq_length,
        dtype = dtype,
        load_in_4bit = load_in_4bit,
    )
    
    # 3. 添加LoRA适配器
    print("添加LoRA适配器...")
    model = FastLanguageModel.get_peft_model(
        model,
        r = 16,
        target_modules = ["q_proj", "k_proj", "v_proj", "o_proj",
                          "gate_proj", "up_proj", "down_proj",],
        lora_alpha = 16,
        lora_dropout = 0,
        bias = "none",
        use_gradient_checkpointing = "unsloth",
        random_state = 3407,
        use_rslora = False,
        loftq_config = None,
    )
    
    # 4. 加载数据集
    print("加载数据集...")
    dataset = load_from_disk("./Chinese-Law-Sample-3K-Thinking")
    print(f"数据集大小: {len(dataset)}")
    
    # 5. 数据格式化函数
    def formatting_prompts_func(examples):
        convos = examples["conversations"]
        texts = []
        for convo in convos:
            text = tokenizer.apply_chat_template(
                convo, 
                tokenize = False, 
                add_generation_prompt = False,
                enable_thinking = True  # 启用thinking模式
            )
            texts.append(text)
        return {"text": texts}
    
    print("格式化数据集...")
    dataset = dataset.map(formatting_prompts_func, batched=True)
    
    # 6. 训练配置
    print("配置训练参数...")
    trainer = SFTTrainer(
        model = model,
        tokenizer = tokenizer,
        train_dataset = dataset,
        dataset_text_field = "text",
        max_seq_length = max_seq_length,
        dataset_num_proc = 2,
        packing = False,
        args = TrainingArguments(
            per_device_train_batch_size = 2,
            gradient_accumulation_steps = 4,
            warmup_steps = 10,
            max_steps = 400,  # 增加训练步数以学习thinking模式
            learning_rate = 2e-4,
            fp16 = not torch.cuda.is_bf16_supported(),
            bf16 = torch.cuda.is_bf16_supported(),
            logging_steps = 10,
            optim = "adamw_8bit",
            weight_decay = 0.01,
            lr_scheduler_type = "linear",
            seed = 3407,
            output_dir = "outputs",
            save_steps = 50,
            save_total_limit = 3,
            dataloader_num_workers = 2,
        ),
    )
    
    # 7. 开始训练
    print("开始训练...")
    trainer_stats = trainer.train()
    
    # 8. 保存模型
    print("保存模型...")
    model.save_pretrained("chinese_law_3k_thinking_model")
    tokenizer.save_pretrained("chinese_law_3k_thinking_model")
    
    print(f"训练完成！模型已保存到: chinese_law_3k_thinking_model")
    
    # 9. 测试模型
    print("\n测试微调后的模型...")
    FastLanguageModel.for_inference(model)
    
    messages = [
        {"role": "user", "content": "请解释合同法中违约责任的承担方式有哪些？"}
    ]
    
    inputs = tokenizer.apply_chat_template(
        messages,
        tokenize = True,
        add_generation_prompt = True,
        enable_thinking = True,
        return_tensors = "pt"
    ).to("cuda")
    
    outputs = model.generate(
        input_ids = inputs,
        max_new_tokens = 512,
        use_cache = True,
        temperature = 0.7,
        top_p = 0.9,
    )
    
    response = tokenizer.batch_decode(outputs)
    print("模型回复:")
    print(response[0])

if __name__ == "__main__":
    main()
